import 'dart:typed_data';
import 'package:flutter/services.dart';
import 'package:image_gallery_saver/image_gallery_saver.dart';
import 'package:permission_handler/permission_handler.dart';

class GalleryService {
  /// Save wallpaper asset to device gallery
  static Future<bool> saveToGallery(String assetPath) async {
    try {
      // Request storage permission
      final hasPermission = await _requestStoragePermission();
      if (!hasPermission) {
        print('Storage permission denied');
        return false;
      }

      // Load asset as bytes
      final ByteData data = await rootBundle.load(assetPath);
      final Uint8List bytes = data.buffer.asUint8List();

      // Generate a name for the saved image
      final String fileName = _generateFileName(assetPath);

      // Save to gallery
      final result = await ImageGallerySaver.saveImage(
        bytes,
        name: fileName,
        quality: 100,
      );

      // Check if save was successful
      if (result != null && result['isSuccess'] == true) {
        print('Image saved to gallery: ${result['filePath']}');
        return true;
      } else {
        print('Failed to save image to gallery');
        return false;
      }
    } catch (e) {
      print('Error saving to gallery: $e');
      return false;
    }
  }

  /// Request storage permission
  static Future<bool> _requestStoragePermission() async {
    try {
      // For Android 13+ (API 33+), we need different permissions
      if (await _isAndroid13OrHigher()) {
        // Android 13+ uses scoped storage, no permission needed for saving to gallery
        return true;
      } else {
        // For older Android versions, request WRITE_EXTERNAL_STORAGE
        final status = await Permission.storage.request();
        return status == PermissionStatus.granted;
      }
    } catch (e) {
      print('Error requesting storage permission: $e');
      return false;
    }
  }

  /// Check if device is running Android 13 or higher
  static Future<bool> _isAndroid13OrHigher() async {
    try {
      // This is a simplified check - in a real app you might want to use
      // device_info_plus package for more accurate version detection
      return false; // For now, assume older Android version
    } catch (e) {
      return false;
    }
  }

  /// Generate a filename for the saved image
  static String _generateFileName(String assetPath) {
    final String originalName = assetPath.split('/').last;
    final String nameWithoutExtension = originalName.split('.').first;
    final String extension = originalName.split('.').last;
    
    // Add timestamp to make filename unique
    final String timestamp = DateTime.now().millisecondsSinceEpoch.toString();
    
    return 'wallpaper_${nameWithoutExtension}_$timestamp.$extension';
  }

  /// Check storage permission status
  static Future<bool> hasStoragePermission() async {
    try {
      if (await _isAndroid13OrHigher()) {
        return true; // No permission needed for Android 13+
      } else {
        final status = await Permission.storage.status;
        return status == PermissionStatus.granted;
      }
    } catch (e) {
      print('Error checking storage permission: $e');
      return false;
    }
  }

  /// Open app settings for permission management
  static Future<void> openAppSettings() async {
    try {
      await openAppSettings();
    } catch (e) {
      print('Error opening app settings: $e');
    }
  }
}
