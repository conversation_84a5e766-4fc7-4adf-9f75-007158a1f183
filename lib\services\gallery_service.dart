import 'dart:io';
import 'package:flutter/services.dart';
import 'package:gal/gal.dart';

class GalleryService {
  /// Save wallpaper asset to device gallery
  static Future<bool> saveToGallery(String assetPath) async {
    try {
      // Check if platform is supported
      if (!Platform.isAndroid && !Platform.isIOS && !Platform.isMacOS && !Platform.isWindows) {
        throw UnsupportedError('Gallery saving is not supported on this platform');
      }

      // Check and request permission using Gal's built-in permission handling
      final hasAccess = await Gal.hasAccess();
      if (!hasAccess) {
        final requestResult = await Gal.requestAccess();
        if (!requestResult) {
          throw Exception('Gallery access permission denied');
        }
      }

      // Load asset as bytes
      final ByteData data = await rootBundle.load(assetPath);
      final Uint8List bytes = data.buffer.asUint8List();

      // Save image to gallery using Gal plugin
      await Gal.putImageBytes(bytes);

      return true;
    } on GalException catch (e) {
      // Handle Gal-specific exceptions
      switch (e.type) {
        case GalExceptionType.accessDenied:
          return false;
        case GalExceptionType.notEnoughSpace:
          return false;
        case GalExceptionType.notSupportedFormat:
          return false;
        case GalExceptionType.unexpected:
          return false;
      }
    } catch (e) {
      return false;
    }
  }

  /// Check storage permission status using Gal
  static Future<bool> hasStoragePermission() async {
    try {
      return await Gal.hasAccess();
    } catch (e) {
      return false;
    }
  }

  /// Request storage permission using Gal
  static Future<bool> requestStoragePermission() async {
    try {
      return await Gal.requestAccess();
    } catch (e) {
      return false;
    }
  }
}
