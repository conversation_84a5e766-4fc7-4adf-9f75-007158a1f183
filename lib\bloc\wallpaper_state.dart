import 'package:equatable/equatable.dart';

abstract class WallpaperState extends Equatable {
  const WallpaperState();

  @override
  List<Object?> get props => [];
}

/// Initial state
class WallpaperInitial extends WallpaperState {
  const WallpaperInitial();
}

/// Loading state
class WallpaperLoading extends WallpaperState {
  const WallpaperLoading();
}

/// Loaded state with wallpapers and favorites
class WallpaperLoaded extends WallpaperState {
  final List<String> wallpapers;
  final List<String> favorites;
  final List<String> filteredWallpapers;
  final String? filterQuery;

  const WallpaperLoaded({
    required this.wallpapers,
    required this.favorites,
    List<String>? filteredWallpapers,
    this.filterQuery,
  }) : filteredWallpapers = filteredWallpapers ?? wallpapers;

  /// Get the list of wallpapers to display (filtered or all)
  List<String> get displayWallpapers => 
      filterQuery?.isNotEmpty == true ? filteredWallpapers : wallpapers;

  /// Check if a wallpaper is favorite
  bool isFavorite(String assetPath) => favorites.contains(assetPath);

  /// Copy with new values
  WallpaperLoaded copyWith({
    List<String>? wallpapers,
    List<String>? favorites,
    List<String>? filteredWallpapers,
    String? filterQuery,
  }) {
    return WallpaperLoaded(
      wallpapers: wallpapers ?? this.wallpapers,
      favorites: favorites ?? this.favorites,
      filteredWallpapers: filteredWallpapers ?? this.filteredWallpapers,
      filterQuery: filterQuery ?? this.filterQuery,
    );
  }

  @override
  List<Object?> get props => [wallpapers, favorites, filteredWallpapers, filterQuery];
}

/// Error state
class WallpaperError extends WallpaperState {
  final String message;

  const WallpaperError(this.message);

  @override
  List<Object?> get props => [message];
}

/// Operation in progress state (for setting wallpaper, saving to gallery, etc.)
class WallpaperOperationInProgress extends WallpaperState {
  final String operation;
  final String? assetPath;

  const WallpaperOperationInProgress({
    required this.operation,
    this.assetPath,
  });

  @override
  List<Object?> get props => [operation, assetPath];
}

/// Operation success state
class WallpaperOperationSuccess extends WallpaperState {
  final String message;
  final String operation;

  const WallpaperOperationSuccess({
    required this.message,
    required this.operation,
  });

  @override
  List<Object?> get props => [message, operation];
}

/// Operation failure state
class WallpaperOperationFailure extends WallpaperState {
  final String message;
  final String operation;

  const WallpaperOperationFailure({
    required this.message,
    required this.operation,
  });

  @override
  List<Object?> get props => [message, operation];
}
