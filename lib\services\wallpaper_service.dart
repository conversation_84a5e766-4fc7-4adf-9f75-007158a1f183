import 'dart:io';
import 'dart:typed_data';
import 'package:flutter/services.dart';
import 'package:flutter_wallpaper_manager/flutter_wallpaper_manager.dart';
import 'package:path_provider/path_provider.dart';
import 'package:permission_handler/permission_handler.dart';

enum WallpaperLocation {
  homeScreen,
  lockScreen,
  both,
}

class WallpaperService {
  /// Set wallpaper from asset path
  static Future<bool> setWallpaperFromAsset(
    String assetPath,
    WallpaperLocation location,
  ) async {
    try {
      // Request permission first
      final hasPermission = await _requestWallpaperPermission();
      if (!hasPermission) {
        print('Wallpaper permission denied');
        return false;
      }

      // Load asset as bytes
      final ByteData data = await rootBundle.load(assetPath);
      final Uint8List bytes = data.buffer.asUint8List();

      // Create temporary file
      final tempFile = await _createTempFile(bytes, assetPath);
      if (tempFile == null) {
        print('Failed to create temporary file');
        return false;
      }

      // Set wallpaper based on location
      int wallpaperLocation;
      switch (location) {
        case WallpaperLocation.homeScreen:
          wallpaperLocation = WallpaperManager.HOME_SCREEN;
          break;
        case WallpaperLocation.lockScreen:
          wallpaperLocation = WallpaperManager.LOCK_SCREEN;
          break;
        case WallpaperLocation.both:
          wallpaperLocation = WallpaperManager.BOTH_SCREEN;
          break;
      }

      final result = await WallpaperManager.setWallpaperFromFile(
        tempFile.path,
        wallpaperLocation,
      );

      // Clean up temporary file
      await tempFile.delete();

      return result;
    } catch (e) {
      print('Error setting wallpaper: $e');
      return false;
    }
  }

  /// Request wallpaper permission
  static Future<bool> _requestWallpaperPermission() async {
    // On Android, SET_WALLPAPER permission is automatically granted
    // but we might need storage permissions for some operations
    return true;
  }

  /// Create temporary file from bytes
  static Future<File?> _createTempFile(Uint8List bytes, String assetPath) async {
    try {
      final tempDir = await getTemporaryDirectory();
      final fileName = assetPath.split('/').last;
      final tempFile = File('${tempDir.path}/$fileName');
      
      await tempFile.writeAsBytes(bytes);
      return tempFile;
    } catch (e) {
      print('Error creating temp file: $e');
      return null;
    }
  }

  /// Get wallpaper location display name
  static String getLocationDisplayName(WallpaperLocation location) {
    switch (location) {
      case WallpaperLocation.homeScreen:
        return 'Home Screen';
      case WallpaperLocation.lockScreen:
        return 'Lock Screen';
      case WallpaperLocation.both:
        return 'Both Screens';
    }
  }
}
