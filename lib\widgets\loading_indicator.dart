import 'package:flutter/material.dart';

class LoadingIndicator extends StatelessWidget {
  final String? message;
  final double size;
  final Color? color;

  const LoadingIndicator({
    super.key,
    this.message,
    this.size = 50.0,
    this.color,
  });

  @override
  Widget build(BuildContext context) {
    return Center(
      child: Column(
        mainAxisAlignment: MainAxisAlignment.center,
        children: [
          SizedBox(
            width: size,
            height: size,
            child: CircularProgressIndicator(
              color: color ?? Theme.of(context).primaryColor,
              strokeWidth: 3.0,
            ),
          ),
          if (message != null) ...[
            const SizedBox(height: 16),
            Text(
              message!,
              style: TextStyle(
                fontSize: 16,
                color: Colors.grey[600],
              ),
              textAlign: TextAlign.center,
            ),
          ],
        ],
      ),
    );
  }
}

class WallpaperLoadingIndicator extends StatelessWidget {
  const WallpaperLoadingIndicator({super.key});

  @override
  Widget build(BuildContext context) {
    return const LoadingIndicator(
      message: 'Loading wallpapers...',
      size: 60.0,
    );
  }
}

class OperationLoadingIndicator extends StatelessWidget {
  final String operation;
  final String? assetPath;

  const OperationLoadingIndicator({
    super.key,
    required this.operation,
    this.assetPath,
  });

  @override
  Widget build(BuildContext context) {
    String message;
    switch (operation) {
      case 'set_wallpaper':
        message = 'Setting wallpaper...';
        break;
      case 'save_to_gallery':
        message = 'Saving to gallery...';
        break;
      case 'toggle_favorite':
        message = 'Updating favorites...';
        break;
      case 'clear_favorites':
        message = 'Clearing favorites...';
        break;
      default:
        message = 'Processing...';
    }

    return Container(
      color: Colors.black.withOpacity(0.5),
      child: LoadingIndicator(
        message: message,
        size: 50.0,
        color: Colors.white,
      ),
    );
  }
}

class GridLoadingShimmer extends StatefulWidget {
  const GridLoadingShimmer({super.key});

  @override
  State<GridLoadingShimmer> createState() => _GridLoadingShimmerState();
}

class _GridLoadingShimmerState extends State<GridLoadingShimmer>
    with SingleTickerProviderStateMixin {
  late AnimationController _animationController;
  late Animation<double> _animation;

  @override
  void initState() {
    super.initState();
    _animationController = AnimationController(
      duration: const Duration(milliseconds: 1500),
      vsync: this,
    );
    _animation = Tween<double>(
      begin: -1.0,
      end: 2.0,
    ).animate(CurvedAnimation(
      parent: _animationController,
      curve: Curves.easeInOut,
    ));
    _animationController.repeat();
  }

  @override
  void dispose() {
    _animationController.dispose();
    super.dispose();
  }

  @override
  Widget build(BuildContext context) {
    return GridView.builder(
      padding: const EdgeInsets.all(8.0),
      gridDelegate: const SliverGridDelegateWithFixedCrossAxisCount(
        crossAxisCount: 2,
        crossAxisSpacing: 8.0,
        mainAxisSpacing: 8.0,
        childAspectRatio: 0.6,
      ),
      itemCount: 6, // Show 6 shimmer items
      itemBuilder: (context, index) {
        return AnimatedBuilder(
          animation: _animation,
          builder: (context, child) {
            return Card(
              elevation: 4,
              clipBehavior: Clip.antiAlias,
              child: Container(
                decoration: BoxDecoration(
                  gradient: LinearGradient(
                    begin: Alignment.topLeft,
                    end: Alignment.bottomRight,
                    colors: [
                      Colors.grey[300]!,
                      Colors.grey[100]!,
                      Colors.grey[300]!,
                    ],
                    stops: [
                      (_animation.value - 1).clamp(0.0, 1.0),
                      _animation.value.clamp(0.0, 1.0),
                      (_animation.value + 1).clamp(0.0, 1.0),
                    ],
                  ),
                ),
              ),
            );
          },
        );
      },
    );
  }
}
