import 'dart:io';
import 'package:flutter/foundation.dart';
import 'package:flutter/services.dart';
import 'package:path_provider/path_provider.dart';
import 'package:wallpaper_manager_flutter/wallpaper_manager_flutter.dart';

enum WallpaperLocation {
  homeScreen,
  lockScreen,
  both,
}

class WallpaperService {
  /// Set wallpaper from asset path
  static Future<bool> setWallpaperFromAsset(
    String assetPath,
    WallpaperLocation location,
  ) async {
    try {
      // Check if platform is supported
      if (!Platform.isAndroid) {
        throw UnsupportedError('Wallpaper setting is only supported on Android');
      }

      // Request permission first
      final hasPermission = await _requestWallpaperPermission();
      if (!hasPermission) {
        throw Exception('Wallpaper permission denied');
      }

      // Load asset as bytes
      final ByteData data = await rootBundle.load(assetPath);
      final Uint8List bytes = data.buffer.asUint8List();

      // Create temporary file
      final tempFile = await _createTempFile(bytes, assetPath);
      if (tempFile == null) {
        print('Failed to create temporary file');
        return false;
      }

      // Set wallpaper using wallpaper_manager_flutter plugin
      final wallpaperManager = WallpaperManagerFlutter();

      // Determine the wallpaper location constant
      int wallpaperLocation;
      switch (location) {
        case WallpaperLocation.homeScreen:
          wallpaperLocation = WallpaperManagerFlutter.homeScreen;
          break;
        case WallpaperLocation.lockScreen:
          wallpaperLocation = WallpaperManagerFlutter.lockScreen;
          break;
        case WallpaperLocation.both:
          wallpaperLocation = WallpaperManagerFlutter.bothScreens;
          break;
      }

      // Set the wallpaper
      final result = await wallpaperManager.setWallpaper(
        tempFile,
        wallpaperLocation,
      );

      // Clean up temporary file
      await tempFile.delete();

      return result;
    } catch (e) {
      print('Error setting wallpaper: $e');
      return false;
    }
  }

  /// Request wallpaper permission
  static Future<bool> _requestWallpaperPermission() async {
    // On Android, SET_WALLPAPER permission is automatically granted
    // The wallpaper_manager_flutter plugin handles this internally
    return true;
  }

  /// Create temporary file from bytes
  static Future<File?> _createTempFile(Uint8List bytes, String assetPath) async {
    try {
      final tempDir = await getTemporaryDirectory();
      final fileName = assetPath.split('/').last;
      final tempFile = File('${tempDir.path}/$fileName');
      
      await tempFile.writeAsBytes(bytes);
      return tempFile;
    } catch (e) {
      print('Error creating temp file: $e');
      return null;
    }
  }

  /// Get wallpaper location display name
  static String getLocationDisplayName(WallpaperLocation location) {
    switch (location) {
      case WallpaperLocation.homeScreen:
        return 'Home Screen';
      case WallpaperLocation.lockScreen:
        return 'Lock Screen';
      case WallpaperLocation.both:
        return 'Both Screens';
    }
  }
}
