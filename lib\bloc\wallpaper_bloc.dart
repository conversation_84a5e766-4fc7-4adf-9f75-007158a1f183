import 'package:flutter_bloc/flutter_bloc.dart';
import 'wallpaper_event.dart';
import 'wallpaper_state.dart';
import '../services/asset_service.dart';
import '../services/favorites_service.dart';
import '../services/wallpaper_service.dart';
import '../services/gallery_service.dart';

class WallpaperBloc extends Bloc<WallpaperEvent, WallpaperState> {
  WallpaperBloc() : super(const WallpaperInitial()) {
    on<LoadWallpapersFromAssets>(_onLoadWallpapersFromAssets);
    on<RefreshWallpapers>(_onRefreshWallpapers);
    on<ToggleFavorite>(_onToggleFavorite);
    on<LoadFavorites>(_onLoadFavorites);
    on<SetWallpaper>(_onSetWallpaper);
    on<SaveToGallery>(_onSaveToGallery);
    on<ClearAllFavorites>(_onClearAllFavorites);
    on<FilterWallpapers>(_onFilterWallpapers);
  }

  /// Load wallpapers from assets
  Future<void> _onLoadWallpapersFromAssets(
    LoadWallpapersFromAssets event,
    Emitter<WallpaperState> emit,
  ) async {
    emit(const WallpaperLoading());
    
    try {
      final wallpapers = await AssetService.getWallpaperAssets();
      final favorites = await FavoritesService.getFavorites();
      
      if (wallpapers.isEmpty) {
        emit(const WallpaperError('No wallpapers found in assets'));
        return;
      }
      
      emit(WallpaperLoaded(
        wallpapers: wallpapers,
        favorites: favorites,
      ));
    } catch (e) {
      emit(WallpaperError('Failed to load wallpapers: $e'));
    }
  }

  /// Refresh wallpapers
  Future<void> _onRefreshWallpapers(
    RefreshWallpapers event,
    Emitter<WallpaperState> emit,
  ) async {
    add(const LoadWallpapersFromAssets());
  }

  /// Toggle favorite status
  Future<void> _onToggleFavorite(
    ToggleFavorite event,
    Emitter<WallpaperState> emit,
  ) async {
    if (state is WallpaperLoaded) {
      final currentState = state as WallpaperLoaded;
      
      try {
        final success = await FavoritesService.toggleFavorite(event.assetPath);
        
        if (success) {
          final updatedFavorites = await FavoritesService.getFavorites();
          emit(currentState.copyWith(favorites: updatedFavorites));
        }
      } catch (e) {
        emit(WallpaperOperationFailure(
          message: 'Failed to update favorite: $e',
          operation: 'toggle_favorite',
        ));
        
        // Return to previous state after showing error
        Future.delayed(const Duration(seconds: 2), () {
          if (!emit.isDone) {
            emit(currentState);
          }
        });
      }
    }
  }

  /// Load favorites
  Future<void> _onLoadFavorites(
    LoadFavorites event,
    Emitter<WallpaperState> emit,
  ) async {
    if (state is WallpaperLoaded) {
      final currentState = state as WallpaperLoaded;

      try {
        final favorites = await FavoritesService.getFavorites();
        emit(currentState.copyWith(favorites: favorites));
      } catch (e) {
        emit(WallpaperOperationFailure(
          message: 'Failed to load favorites: $e',
          operation: 'load_favorites',
        ));
      }
    }
  }

  /// Set wallpaper
  Future<void> _onSetWallpaper(
    SetWallpaper event,
    Emitter<WallpaperState> emit,
  ) async {
    final currentState = state;

    emit(WallpaperOperationInProgress(
      operation: 'set_wallpaper',
      assetPath: event.assetPath,
    ));

    try {
      final success = await WallpaperService.setWallpaperFromAsset(
        event.assetPath,
        event.location,
      );

      if (success) {
        emit(WallpaperOperationSuccess(
          message: 'Wallpaper set successfully!',
          operation: 'set_wallpaper',
        ));
      } else {
        emit(const WallpaperOperationFailure(
          message: 'Failed to set wallpaper',
          operation: 'set_wallpaper',
        ));
      }

      // Return to previous state after 2 seconds
      Future.delayed(const Duration(seconds: 2), () {
        if (!emit.isDone && currentState is WallpaperLoaded) {
          emit(currentState);
        }
      });
    } catch (e) {
      emit(WallpaperOperationFailure(
        message: 'Error setting wallpaper: $e',
        operation: 'set_wallpaper',
      ));

      // Return to previous state after 2 seconds
      Future.delayed(const Duration(seconds: 2), () {
        if (!emit.isDone && currentState is WallpaperLoaded) {
          emit(currentState);
        }
      });
    }
  }

  /// Save to gallery
  Future<void> _onSaveToGallery(
    SaveToGallery event,
    Emitter<WallpaperState> emit,
  ) async {
    final currentState = state;

    emit(WallpaperOperationInProgress(
      operation: 'save_to_gallery',
      assetPath: event.assetPath,
    ));

    try {
      final success = await GalleryService.saveToGallery(event.assetPath);

      if (success) {
        emit(const WallpaperOperationSuccess(
          message: 'Image saved to gallery!',
          operation: 'save_to_gallery',
        ));
      } else {
        emit(const WallpaperOperationFailure(
          message: 'Failed to save image to gallery',
          operation: 'save_to_gallery',
        ));
      }

      // Return to previous state after 2 seconds
      Future.delayed(const Duration(seconds: 2), () {
        if (!emit.isDone && currentState is WallpaperLoaded) {
          emit(currentState);
        }
      });
    } catch (e) {
      emit(WallpaperOperationFailure(
        message: 'Error saving to gallery: $e',
        operation: 'save_to_gallery',
      ));

      // Return to previous state after 2 seconds
      Future.delayed(const Duration(seconds: 2), () {
        if (!emit.isDone && currentState is WallpaperLoaded) {
          emit(currentState);
        }
      });
    }
  }

  /// Clear all favorites
  Future<void> _onClearAllFavorites(
    ClearAllFavorites event,
    Emitter<WallpaperState> emit,
  ) async {
    if (state is WallpaperLoaded) {
      final currentState = state as WallpaperLoaded;

      try {
        final success = await FavoritesService.clearAllFavorites();

        if (success) {
          emit(currentState.copyWith(favorites: []));
          emit(const WallpaperOperationSuccess(
            message: 'All favorites cleared!',
            operation: 'clear_favorites',
          ));

          // Return to loaded state after 2 seconds
          Future.delayed(const Duration(seconds: 2), () {
            if (!emit.isDone) {
              emit(currentState.copyWith(favorites: []));
            }
          });
        }
      } catch (e) {
        emit(WallpaperOperationFailure(
          message: 'Failed to clear favorites: $e',
          operation: 'clear_favorites',
        ));
      }
    }
  }

  /// Filter wallpapers
  Future<void> _onFilterWallpapers(
    FilterWallpapers event,
    Emitter<WallpaperState> emit,
  ) async {
    if (state is WallpaperLoaded) {
      final currentState = state as WallpaperLoaded;

      if (event.query.isEmpty) {
        emit(currentState.copyWith(
          filteredWallpapers: currentState.wallpapers,
          filterQuery: '',
        ));
        return;
      }

      final filteredWallpapers = currentState.wallpapers
          .where((wallpaper) {
            final displayName = AssetService.getDisplayName(wallpaper).toLowerCase();
            return displayName.contains(event.query.toLowerCase());
          })
          .toList();

      emit(currentState.copyWith(
        filteredWallpapers: filteredWallpapers,
        filterQuery: event.query,
      ));
    }
  }
}
