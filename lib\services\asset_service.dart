import 'dart:convert';
import 'package:flutter/services.dart';

class AssetService {
  static const String _assetPath = 'assets/images/';
  
  /// Get list of all wallpaper assets from the asset manifest
  static Future<List<String>> getWallpaperAssets() async {
    try {
      // Load the asset manifest
      final manifestContent = await rootBundle.loadString('AssetManifest.json');
      final Map<String, dynamic> manifestMap = json.decode(manifestContent);
      
      // Filter assets that are in the images directory and are image files
      final List<String> wallpaperAssets = manifestMap.keys
          .where((String key) => 
              key.startsWith(_assetPath) && 
              _isImageFile(key))
          .toList();
      
      // Sort the assets for consistent ordering
      wallpaperAssets.sort();
      
      return wallpaperAssets;
    } catch (e) {
      print('Error loading wallpaper assets: $e');
      return [];
    }
  }
  
  /// Check if a file is an image based on its extension
  static bool _isImageFile(String path) {
    final String extension = path.toLowerCase().split('.').last;
    return ['jpg', 'jpeg', 'png', 'webp', 'gif'].contains(extension);
  }
  
  /// Get asset as bytes for saving to gallery
  static Future<Uint8List?> getAssetBytes(String assetPath) async {
    try {
      final ByteData data = await rootBundle.load(assetPath);
      return data.buffer.asUint8List();
    } catch (e) {
      print('Error loading asset bytes: $e');
      return null;
    }
  }
  
  /// Get a display name for the wallpaper from its asset path
  static String getDisplayName(String assetPath) {
    final String fileName = assetPath.split('/').last;
    final String nameWithoutExtension = fileName.split('.').first;
    
    // Convert snake_case or kebab-case to Title Case
    return nameWithoutExtension
        .replaceAll('_', ' ')
        .replaceAll('-', ' ')
        .split(' ')
        .map((word) => word.isNotEmpty 
            ? word[0].toUpperCase() + word.substring(1).toLowerCase()
            : '')
        .join(' ');
  }
}
