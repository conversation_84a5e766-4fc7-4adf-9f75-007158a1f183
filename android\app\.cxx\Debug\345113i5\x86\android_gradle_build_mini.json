{"buildFiles": ["C:\\dev\\flutter\\packages\\flutter_tools\\gradle\\src\\main\\groovy\\CMakeLists.txt"], "cleanCommandsComponents": [["C:\\Users\\<USER>\\AppData\\Local\\Android\\sdk\\cmake\\3.22.1\\bin\\ninja.exe", "-C", "E:\\My_Flutter_Apps\\wallpaper_app\\android\\app\\.cxx\\Debug\\345113i5\\x86", "clean"]], "buildTargetsCommandComponents": ["C:\\Users\\<USER>\\AppData\\Local\\Android\\sdk\\cmake\\3.22.1\\bin\\ninja.exe", "-C", "E:\\My_Flutter_Apps\\wallpaper_app\\android\\app\\.cxx\\Debug\\345113i5\\x86", "{LIST_OF_TARGETS_TO_BUILD}"], "libraries": {}}