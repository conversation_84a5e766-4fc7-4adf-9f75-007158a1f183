// This is a basic Flutter widget test.
//
// To perform an interaction with a widget in your test, use the WidgetTester
// utility in the flutter_test package. For example, you can send tap and scroll
// gestures. You can also use WidgetTester to find child widgets in the widget
// tree, read text, and verify that the values of widget properties are correct.

import 'package:flutter/material.dart';
import 'package:flutter_test/flutter_test.dart';

import 'package:wallpaper_app/main.dart';

void main() {
  testWidgets('Wallpaper app loads correctly', (WidgetTester tester) async {
    // Build our app and trigger a frame.
    await tester.pumpWidget(const WallpaperApp());

    // Verify that the app loads with the correct title
    expect(find.text('Wallpapers'), findsOneWidget);

    // Wait for the app to load wallpapers
    await tester.pump();

    // The app should show either wallpapers or a loading indicator
    expect(
      find.byType(CircularProgressIndicator).or(find.text('No wallpapers found')),
      findsOneWidget,
    );
  });
}
