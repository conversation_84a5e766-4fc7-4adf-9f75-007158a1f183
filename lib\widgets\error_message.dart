import 'package:flutter/material.dart';

class ErrorMessage extends StatelessWidget {
  final String message;
  final VoidCallback? onRetry;
  final IconData? icon;
  final String? retryButtonText;

  const ErrorMessage({
    super.key,
    required this.message,
    this.onRetry,
    this.icon,
    this.retryButtonText,
  });

  @override
  Widget build(BuildContext context) {
    return Center(
      child: Padding(
        padding: const EdgeInsets.all(24.0),
        child: Column(
          mainAxisAlignment: MainAxisAlignment.center,
          children: [
            Icon(
              icon ?? Icons.error_outline,
              size: 64,
              color: Colors.red[400],
            ),
            const SizedBox(height: 16),
            Text(
              'Oops! Something went wrong',
              style: Theme.of(context).textTheme.headlineSmall?.copyWith(
                color: Colors.grey[800],
                fontWeight: FontWeight.w600,
              ),
              textAlign: TextAlign.center,
            ),
            const SizedBox(height: 8),
            Text(
              message,
              style: Theme.of(context).textTheme.bodyMedium?.copyWith(
                color: Colors.grey[600],
              ),
              textAlign: TextAlign.center,
            ),
            if (onRetry != null) ...[
              const SizedBox(height: 24),
              ElevatedButton.icon(
                onPressed: onRetry,
                icon: const Icon(Icons.refresh),
                label: Text(retryButtonText ?? 'Try Again'),
                style: ElevatedButton.styleFrom(
                  padding: const EdgeInsets.symmetric(
                    horizontal: 24,
                    vertical: 12,
                  ),
                ),
              ),
            ],
          ],
        ),
      ),
    );
  }
}

class WallpaperErrorMessage extends StatelessWidget {
  final String message;
  final VoidCallback? onRetry;

  const WallpaperErrorMessage({
    super.key,
    required this.message,
    this.onRetry,
  });

  @override
  Widget build(BuildContext context) {
    return ErrorMessage(
      message: message,
      onRetry: onRetry,
      icon: Icons.image_not_supported,
      retryButtonText: 'Reload Wallpapers',
    );
  }
}

class OperationErrorMessage extends StatelessWidget {
  final String message;
  final String operation;
  final VoidCallback? onDismiss;

  const OperationErrorMessage({
    super.key,
    required this.message,
    required this.operation,
    this.onDismiss,
  });

  @override
  Widget build(BuildContext context) {
    IconData icon;
    String title;
    
    switch (operation) {
      case 'set_wallpaper':
        icon = Icons.wallpaper;
        title = 'Failed to Set Wallpaper';
        break;
      case 'save_to_gallery':
        icon = Icons.save_alt;
        title = 'Failed to Save Image';
        break;
      case 'toggle_favorite':
        icon = Icons.favorite_border;
        title = 'Failed to Update Favorites';
        break;
      case 'clear_favorites':
        icon = Icons.clear_all;
        title = 'Failed to Clear Favorites';
        break;
      default:
        icon = Icons.error_outline;
        title = 'Operation Failed';
    }

    return Container(
      color: Colors.black.withOpacity(0.5),
      child: Center(
        child: Card(
          margin: const EdgeInsets.all(24),
          child: Padding(
            padding: const EdgeInsets.all(24),
            child: Column(
              mainAxisSize: MainAxisSize.min,
              children: [
                Icon(
                  icon,
                  size: 48,
                  color: Colors.red[400],
                ),
                const SizedBox(height: 16),
                Text(
                  title,
                  style: Theme.of(context).textTheme.titleLarge?.copyWith(
                    fontWeight: FontWeight.w600,
                  ),
                  textAlign: TextAlign.center,
                ),
                const SizedBox(height: 8),
                Text(
                  message,
                  style: Theme.of(context).textTheme.bodyMedium?.copyWith(
                    color: Colors.grey[600],
                  ),
                  textAlign: TextAlign.center,
                ),
                const SizedBox(height: 24),
                ElevatedButton(
                  onPressed: onDismiss ?? () => Navigator.of(context).pop(),
                  child: const Text('OK'),
                ),
              ],
            ),
          ),
        ),
      ),
    );
  }
}

class SuccessMessage extends StatelessWidget {
  final String message;
  final String operation;
  final VoidCallback? onDismiss;

  const SuccessMessage({
    super.key,
    required this.message,
    required this.operation,
    this.onDismiss,
  });

  @override
  Widget build(BuildContext context) {
    IconData icon;
    Color color;
    
    switch (operation) {
      case 'set_wallpaper':
        icon = Icons.wallpaper;
        color = Colors.green;
        break;
      case 'save_to_gallery':
        icon = Icons.save_alt;
        color = Colors.blue;
        break;
      case 'toggle_favorite':
        icon = Icons.favorite;
        color = Colors.red;
        break;
      case 'clear_favorites':
        icon = Icons.clear_all;
        color = Colors.orange;
        break;
      default:
        icon = Icons.check_circle_outline;
        color = Colors.green;
    }

    return Container(
      color: Colors.black.withOpacity(0.5),
      child: Center(
        child: Card(
          margin: const EdgeInsets.all(24),
          child: Padding(
            padding: const EdgeInsets.all(24),
            child: Column(
              mainAxisSize: MainAxisSize.min,
              children: [
                Icon(
                  icon,
                  size: 48,
                  color: color,
                ),
                const SizedBox(height: 16),
                Text(
                  'Success!',
                  style: Theme.of(context).textTheme.titleLarge?.copyWith(
                    fontWeight: FontWeight.w600,
                    color: color,
                  ),
                  textAlign: TextAlign.center,
                ),
                const SizedBox(height: 8),
                Text(
                  message,
                  style: Theme.of(context).textTheme.bodyMedium?.copyWith(
                    color: Colors.grey[600],
                  ),
                  textAlign: TextAlign.center,
                ),
                const SizedBox(height: 24),
                ElevatedButton(
                  onPressed: onDismiss ?? () => Navigator.of(context).pop(),
                  style: ElevatedButton.styleFrom(
                    backgroundColor: color,
                    foregroundColor: Colors.white,
                  ),
                  child: const Text('OK'),
                ),
              ],
            ),
          ),
        ),
      ),
    );
  }
}
