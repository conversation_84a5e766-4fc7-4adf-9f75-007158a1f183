import 'package:flutter/material.dart';
import 'package:flutter_bloc/flutter_bloc.dart';
import '../bloc/wallpaper_bloc.dart';
import '../bloc/wallpaper_event.dart';
import '../bloc/wallpaper_state.dart';
import '../widgets/wallpaper_grid.dart';
import '../widgets/loading_indicator.dart';
import '../widgets/error_message.dart';
import 'wallpaper_detail_page.dart';
import 'favorites_page.dart';

class HomePage extends StatefulWidget {
  const HomePage({super.key});

  @override
  State<HomePage> createState() => _HomePageState();
}

class _HomePageState extends State<HomePage> {
  final TextEditingController _searchController = TextEditingController();

  @override
  void dispose() {
    _searchController.dispose();
    super.dispose();
  }

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      appBar: AppBar(
        title: const Text('Wallpapers'),
        backgroundColor: Theme.of(context).colorScheme.inversePrimary,
        actions: [
          IconButton(
            icon: const Icon(Icons.favorite),
            onPressed: () {
              Navigator.of(context).push(
                MaterialPageRoute(
                  builder: (context) => const FavoritesPage(),
                ),
              );
            },
            tooltip: 'Favorites',
          ),
          IconButton(
            icon: const Icon(Icons.refresh),
            onPressed: () {
              context.read<WallpaperBloc>().add(const RefreshWallpapers());
            },
            tooltip: 'Refresh',
          ),
        ],
      ),
      body: Column(
        children: [
          // Search bar
          Padding(
            padding: const EdgeInsets.all(16.0),
            child: TextField(
              controller: _searchController,
              decoration: InputDecoration(
                hintText: 'Search wallpapers...',
                prefixIcon: const Icon(Icons.search),
                suffixIcon: _searchController.text.isNotEmpty
                    ? IconButton(
                        icon: const Icon(Icons.clear),
                        onPressed: () {
                          _searchController.clear();
                          context.read<WallpaperBloc>().add(
                                const FilterWallpapers(''),
                              );
                        },
                      )
                    : null,
                border: OutlineInputBorder(
                  borderRadius: BorderRadius.circular(12),
                ),
                filled: true,
                fillColor: Colors.grey[100],
              ),
              onChanged: (query) {
                context.read<WallpaperBloc>().add(FilterWallpapers(query));
              },
            ),
          ),
          
          // Main content
          Expanded(
            child: BlocConsumer<WallpaperBloc, WallpaperState>(
              listener: (context, state) {
                if (state is WallpaperOperationInProgress) {
                  // Show loading overlay
                  showDialog(
                    context: context,
                    barrierDismissible: false,
                    builder: (context) => OperationLoadingIndicator(
                      operation: state.operation,
                      assetPath: state.assetPath,
                    ),
                  );
                } else if (state is WallpaperOperationSuccess) {
                  // Dismiss loading and show success
                  Navigator.of(context).pop(); // Dismiss loading
                  ScaffoldMessenger.of(context).showSnackBar(
                    SnackBar(
                      content: Text(state.message),
                      backgroundColor: Colors.green,
                      duration: const Duration(seconds: 2),
                    ),
                  );
                } else if (state is WallpaperOperationFailure) {
                  // Dismiss loading and show error
                  Navigator.of(context).pop(); // Dismiss loading
                  ScaffoldMessenger.of(context).showSnackBar(
                    SnackBar(
                      content: Text(state.message),
                      backgroundColor: Colors.red,
                      duration: const Duration(seconds: 3),
                    ),
                  );
                }
              },
              builder: (context, state) {
                if (state is WallpaperInitial || state is WallpaperLoading) {
                  return const WallpaperLoadingIndicator();
                } else if (state is WallpaperError) {
                  return WallpaperErrorMessage(
                    message: state.message,
                    onRetry: () {
                      context.read<WallpaperBloc>().add(
                            const LoadWallpapersFromAssets(),
                          );
                    },
                  );
                } else if (state is WallpaperLoaded) {
                  return WallpaperGrid(
                    wallpapers: state.displayWallpapers,
                    favorites: state.favorites,
                    onWallpaperTap: (assetPath) {
                      Navigator.of(context).push(
                        MaterialPageRoute(
                          builder: (context) => WallpaperDetailPage(
                            assetPath: assetPath,
                          ),
                        ),
                      );
                    },
                    onFavoriteTap: (assetPath) {
                      context.read<WallpaperBloc>().add(
                            ToggleFavorite(assetPath),
                          );
                    },
                  );
                }
                
                return const Center(
                  child: Text('Unknown state'),
                );
              },
            ),
          ),
        ],
      ),
    );
  }
}
