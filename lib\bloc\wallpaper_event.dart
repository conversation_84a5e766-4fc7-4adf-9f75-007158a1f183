import 'package:equatable/equatable.dart';
import '../services/wallpaper_service.dart';

abstract class WallpaperEvent extends Equatable {
  const WallpaperEvent();

  @override
  List<Object?> get props => [];
}

/// Event to load wallpapers from assets
class LoadWallpapersFromAssets extends WallpaperEvent {
  const LoadWallpapersFromAssets();
}

/// Event to refresh wallpapers
class RefreshWallpapers extends WallpaperEvent {
  const RefreshWallpapers();
}

/// Event to toggle favorite status of a wallpaper
class ToggleFavorite extends WallpaperEvent {
  final String assetPath;

  const ToggleFavorite(this.assetPath);

  @override
  List<Object?> get props => [assetPath];
}

/// Event to load favorites
class LoadFavorites extends WallpaperEvent {
  const LoadFavorites();
}

/// Event to set wallpaper
class SetWallpaper extends WallpaperEvent {
  final String assetPath;
  final WallpaperLocation location;

  const SetWallpaper({
    required this.assetPath,
    required this.location,
  });

  @override
  List<Object?> get props => [assetPath, location];
}

/// Event to save wallpaper to gallery
class SaveToGallery extends WallpaperEvent {
  final String assetPath;

  const SaveToGallery(this.assetPath);

  @override
  List<Object?> get props => [assetPath];
}

/// Event to clear all favorites
class ClearAllFavorites extends WallpaperEvent {
  const ClearAllFavorites();
}

/// Event to filter wallpapers (for future search functionality)
class FilterWallpapers extends WallpaperEvent {
  final String query;

  const FilterWallpapers(this.query);

  @override
  List<Object?> get props => [query];
}
