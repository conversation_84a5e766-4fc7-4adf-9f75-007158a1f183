import 'package:flutter/material.dart';
import 'package:flutter_bloc/flutter_bloc.dart';
import '../bloc/wallpaper_bloc.dart';
import '../bloc/wallpaper_event.dart';
import '../bloc/wallpaper_state.dart';
import '../services/asset_service.dart';
import '../services/wallpaper_service.dart';
import '../widgets/loading_indicator.dart';
import '../widgets/error_message.dart';

class WallpaperDetailPage extends StatelessWidget {
  final String assetPath;

  const WallpaperDetailPage({
    super.key,
    required this.assetPath,
  });

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      backgroundColor: Colors.black,
      appBar: AppBar(
        backgroundColor: Colors.transparent,
        elevation: 0,
        iconTheme: const IconThemeData(color: Colors.white),
        title: Text(
          AssetService.getDisplayName(assetPath),
          style: const TextStyle(color: Colors.white),
        ),
        actions: [
          BlocBuilder<WallpaperBloc, WallpaperState>(
            builder: (context, state) {
              if (state is WallpaperLoaded) {
                final isFavorite = state.isFavorite(assetPath);
                return IconButton(
                  icon: Icon(
                    isFavorite ? Icons.favorite : Icons.favorite_border,
                    color: isFavorite ? Colors.red : Colors.white,
                  ),
                  onPressed: () {
                    context.read<WallpaperBloc>().add(ToggleFavorite(assetPath));
                  },
                  tooltip: isFavorite ? 'Remove from favorites' : 'Add to favorites',
                );
              }
              return const SizedBox.shrink();
            },
          ),
        ],
      ),
      extendBodyBehindAppBar: true,
      body: BlocListener<WallpaperBloc, WallpaperState>(
        listener: (context, state) {
          if (state is WallpaperOperationSuccess) {
            ScaffoldMessenger.of(context).showSnackBar(
              SnackBar(
                content: Text(state.message),
                backgroundColor: Colors.green,
                duration: const Duration(seconds: 2),
              ),
            );
          } else if (state is WallpaperOperationFailure) {
            ScaffoldMessenger.of(context).showSnackBar(
              SnackBar(
                content: Text(state.message),
                backgroundColor: Colors.red,
                duration: const Duration(seconds: 3),
              ),
            );
          }
        },
        child: Stack(
          fit: StackFit.expand,
          children: [
            // Wallpaper image
            Image.asset(
              assetPath,
              fit: BoxFit.cover,
              errorBuilder: (context, error, stackTrace) {
                return Container(
                  color: Colors.grey[900],
                  child: const Center(
                    child: Column(
                      mainAxisAlignment: MainAxisAlignment.center,
                      children: [
                        Icon(
                          Icons.broken_image,
                          size: 64,
                          color: Colors.white,
                        ),
                        SizedBox(height: 16),
                        Text(
                          'Image not found',
                          style: TextStyle(
                            color: Colors.white,
                            fontSize: 18,
                          ),
                        ),
                      ],
                    ),
                  ),
                );
              },
            ),
            
            // Bottom action buttons
            Positioned(
              bottom: 0,
              left: 0,
              right: 0,
              child: Container(
                decoration: BoxDecoration(
                  gradient: LinearGradient(
                    begin: Alignment.bottomCenter,
                    end: Alignment.topCenter,
                    colors: [
                      Colors.black.withOpacity(0.8),
                      Colors.transparent,
                    ],
                  ),
                ),
                padding: const EdgeInsets.all(24),
                child: Column(
                  mainAxisSize: MainAxisSize.min,
                  children: [
                    // Wallpaper actions row 1
                    Row(
                      children: [
                        Expanded(
                          child: _ActionButton(
                            icon: Icons.home,
                            label: 'Set as Home',
                            onPressed: () {
                              context.read<WallpaperBloc>().add(
                                    SetWallpaper(
                                      assetPath: assetPath,
                                      location: WallpaperLocation.homeScreen,
                                    ),
                                  );
                            },
                          ),
                        ),
                        const SizedBox(width: 12),
                        Expanded(
                          child: _ActionButton(
                            icon: Icons.lock,
                            label: 'Set as Lock',
                            onPressed: () {
                              context.read<WallpaperBloc>().add(
                                    SetWallpaper(
                                      assetPath: assetPath,
                                      location: WallpaperLocation.lockScreen,
                                    ),
                                  );
                            },
                          ),
                        ),
                      ],
                    ),
                    const SizedBox(height: 12),
                    
                    // Wallpaper actions row 2
                    Row(
                      children: [
                        Expanded(
                          child: _ActionButton(
                            icon: Icons.wallpaper,
                            label: 'Set as Both',
                            onPressed: () {
                              context.read<WallpaperBloc>().add(
                                    SetWallpaper(
                                      assetPath: assetPath,
                                      location: WallpaperLocation.both,
                                    ),
                                  );
                            },
                          ),
                        ),
                        const SizedBox(width: 12),
                        Expanded(
                          child: _ActionButton(
                            icon: Icons.save_alt,
                            label: 'Save to Gallery',
                            onPressed: () {
                              context.read<WallpaperBloc>().add(
                                    SaveToGallery(assetPath),
                                  );
                            },
                          ),
                        ),
                      ],
                    ),
                  ],
                ),
              ),
            ),
            
            // Loading overlay
            BlocBuilder<WallpaperBloc, WallpaperState>(
              builder: (context, state) {
                if (state is WallpaperOperationInProgress) {
                  return OperationLoadingIndicator(
                    operation: state.operation,
                    assetPath: state.assetPath,
                  );
                }
                return const SizedBox.shrink();
              },
            ),
          ],
        ),
      ),
    );
  }
}

class _ActionButton extends StatelessWidget {
  final IconData icon;
  final String label;
  final VoidCallback onPressed;

  const _ActionButton({
    required this.icon,
    required this.label,
    required this.onPressed,
  });

  @override
  Widget build(BuildContext context) {
    return ElevatedButton.icon(
      onPressed: onPressed,
      icon: Icon(icon, size: 20),
      label: Text(
        label,
        style: const TextStyle(fontSize: 12),
      ),
      style: ElevatedButton.styleFrom(
        backgroundColor: Colors.white.withOpacity(0.9),
        foregroundColor: Colors.black,
        padding: const EdgeInsets.symmetric(vertical: 12, horizontal: 8),
        shape: RoundedRectangleBorder(
          borderRadius: BorderRadius.circular(8),
        ),
      ),
    );
  }
}
