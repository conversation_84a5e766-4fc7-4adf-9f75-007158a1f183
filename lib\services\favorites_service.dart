import 'package:shared_preferences/shared_preferences.dart';

class FavoritesService {
  static const String _favoritesKey = 'favorite_wallpapers';
  
  /// Get list of favorite wallpaper asset paths
  static Future<List<String>> getFavorites() async {
    try {
      final prefs = await SharedPreferences.getInstance();
      final List<String>? favorites = prefs.getStringList(_favoritesKey);
      return favorites ?? [];
    } catch (e) {
      print('Error getting favorites: $e');
      return [];
    }
  }
  
  /// Add wallpaper to favorites
  static Future<bool> addToFavorites(String assetPath) async {
    try {
      final prefs = await SharedPreferences.getInstance();
      final List<String> favorites = await getFavorites();
      
      if (!favorites.contains(assetPath)) {
        favorites.add(assetPath);
        await prefs.setStringList(_favoritesKey, favorites);
        return true;
      }
      
      return false; // Already in favorites
    } catch (e) {
      print('Error adding to favorites: $e');
      return false;
    }
  }
  
  /// Remove wallpaper from favorites
  static Future<bool> removeFromFavorites(String assetPath) async {
    try {
      final prefs = await SharedPreferences.getInstance();
      final List<String> favorites = await getFavorites();
      
      if (favorites.contains(assetPath)) {
        favorites.remove(assetPath);
        await prefs.setStringList(_favoritesKey, favorites);
        return true;
      }
      
      return false; // Not in favorites
    } catch (e) {
      print('Error removing from favorites: $e');
      return false;
    }
  }
  
  /// Check if wallpaper is in favorites
  static Future<bool> isFavorite(String assetPath) async {
    try {
      final List<String> favorites = await getFavorites();
      return favorites.contains(assetPath);
    } catch (e) {
      print('Error checking favorite status: $e');
      return false;
    }
  }
  
  /// Toggle favorite status of a wallpaper
  static Future<bool> toggleFavorite(String assetPath) async {
    try {
      final bool isCurrentlyFavorite = await isFavorite(assetPath);
      
      if (isCurrentlyFavorite) {
        return await removeFromFavorites(assetPath);
      } else {
        return await addToFavorites(assetPath);
      }
    } catch (e) {
      print('Error toggling favorite: $e');
      return false;
    }
  }
  
  /// Clear all favorites
  static Future<bool> clearAllFavorites() async {
    try {
      final prefs = await SharedPreferences.getInstance();
      await prefs.remove(_favoritesKey);
      return true;
    } catch (e) {
      print('Error clearing favorites: $e');
      return false;
    }
  }
  
  /// Get count of favorites
  static Future<int> getFavoritesCount() async {
    try {
      final List<String> favorites = await getFavorites();
      return favorites.length;
    } catch (e) {
      print('Error getting favorites count: $e');
      return 0;
    }
  }
}
