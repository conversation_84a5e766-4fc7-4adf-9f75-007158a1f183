import 'package:flutter/material.dart';
import 'package:flutter_bloc/flutter_bloc.dart';
import '../bloc/wallpaper_bloc.dart';
import '../bloc/wallpaper_event.dart';
import '../bloc/wallpaper_state.dart';
import '../widgets/wallpaper_grid.dart';
import '../widgets/loading_indicator.dart';
import '../widgets/error_message.dart';
import 'wallpaper_detail_page.dart';

class FavoritesPage extends StatelessWidget {
  const FavoritesPage({super.key});

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      appBar: AppBar(
        title: const Text('Favorites'),
        backgroundColor: Theme.of(context).colorScheme.inversePrimary,
        actions: [
          BlocBuilder<WallpaperBloc, WallpaperState>(
            builder: (context, state) {
              if (state is WallpaperLoaded && state.favorites.isNotEmpty) {
                return PopupMenuButton<String>(
                  onSelected: (value) {
                    if (value == 'clear_all') {
                      _showClearAllDialog(context);
                    }
                  },
                  itemBuilder: (context) => [
                    const PopupMenuItem(
                      value: 'clear_all',
                      child: Row(
                        children: [
                          Icon(Icons.clear_all, color: Colors.red),
                          SizedBox(width: 8),
                          Text('Clear All'),
                        ],
                      ),
                    ),
                  ],
                );
              }
              return const SizedBox.shrink();
            },
          ),
        ],
      ),
      body: BlocConsumer<WallpaperBloc, WallpaperState>(
        listener: (context, state) {
          if (state is WallpaperOperationInProgress) {
            // Show loading overlay
            showDialog(
              context: context,
              barrierDismissible: false,
              builder: (context) => OperationLoadingIndicator(
                operation: state.operation,
                assetPath: state.assetPath,
              ),
            );
          } else if (state is WallpaperOperationSuccess) {
            // Dismiss loading and show success
            Navigator.of(context).pop(); // Dismiss loading
            ScaffoldMessenger.of(context).showSnackBar(
              SnackBar(
                content: Text(state.message),
                backgroundColor: Colors.green,
                duration: const Duration(seconds: 2),
              ),
            );
          } else if (state is WallpaperOperationFailure) {
            // Dismiss loading and show error
            Navigator.of(context).pop(); // Dismiss loading
            ScaffoldMessenger.of(context).showSnackBar(
              SnackBar(
                content: Text(state.message),
                backgroundColor: Colors.red,
                duration: const Duration(seconds: 3),
              ),
            );
          }
        },
        builder: (context, state) {
          if (state is WallpaperInitial || state is WallpaperLoading) {
            return const WallpaperLoadingIndicator();
          } else if (state is WallpaperError) {
            return WallpaperErrorMessage(
              message: state.message,
              onRetry: () {
                context.read<WallpaperBloc>().add(
                      const LoadWallpapersFromAssets(),
                    );
              },
            );
          } else if (state is WallpaperLoaded) {
            if (state.favorites.isEmpty) {
              return _buildEmptyFavorites(context);
            }
            
            return WallpaperGrid(
              wallpapers: state.favorites,
              favorites: state.favorites, // All displayed wallpapers are favorites
              onWallpaperTap: (assetPath) {
                Navigator.of(context).push(
                  MaterialPageRoute(
                    builder: (context) => WallpaperDetailPage(
                      assetPath: assetPath,
                    ),
                  ),
                );
              },
              onFavoriteTap: (assetPath) {
                context.read<WallpaperBloc>().add(
                      ToggleFavorite(assetPath),
                    );
              },
            );
          }
          
          return const Center(
            child: Text('Unknown state'),
          );
        },
      ),
    );
  }

  Widget _buildEmptyFavorites(BuildContext context) {
    return Center(
      child: Padding(
        padding: const EdgeInsets.all(24.0),
        child: Column(
          mainAxisAlignment: MainAxisAlignment.center,
          children: [
            Icon(
              Icons.favorite_border,
              size: 80,
              color: Colors.grey[400],
            ),
            const SizedBox(height: 24),
            Text(
              'No Favorites Yet',
              style: Theme.of(context).textTheme.headlineSmall?.copyWith(
                color: Colors.grey[600],
                fontWeight: FontWeight.w600,
              ),
            ),
            const SizedBox(height: 12),
            Text(
              'Start adding wallpapers to your favorites by tapping the heart icon on any wallpaper.',
              style: Theme.of(context).textTheme.bodyMedium?.copyWith(
                color: Colors.grey[500],
              ),
              textAlign: TextAlign.center,
            ),
            const SizedBox(height: 32),
            ElevatedButton.icon(
              onPressed: () {
                Navigator.of(context).pop(); // Go back to home page
              },
              icon: const Icon(Icons.explore),
              label: const Text('Browse Wallpapers'),
              style: ElevatedButton.styleFrom(
                padding: const EdgeInsets.symmetric(
                  horizontal: 24,
                  vertical: 12,
                ),
              ),
            ),
          ],
        ),
      ),
    );
  }

  void _showClearAllDialog(BuildContext context) {
    showDialog(
      context: context,
      builder: (BuildContext dialogContext) {
        return AlertDialog(
          title: const Text('Clear All Favorites'),
          content: const Text(
            'Are you sure you want to remove all wallpapers from your favorites? This action cannot be undone.',
          ),
          actions: [
            TextButton(
              onPressed: () {
                Navigator.of(dialogContext).pop();
              },
              child: const Text('Cancel'),
            ),
            ElevatedButton(
              onPressed: () {
                Navigator.of(dialogContext).pop();
                context.read<WallpaperBloc>().add(const ClearAllFavorites());
              },
              style: ElevatedButton.styleFrom(
                backgroundColor: Colors.red,
                foregroundColor: Colors.white,
              ),
              child: const Text('Clear All'),
            ),
          ],
        );
      },
    );
  }
}
