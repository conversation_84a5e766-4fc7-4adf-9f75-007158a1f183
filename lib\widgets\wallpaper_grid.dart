import 'package:flutter/material.dart';
import '../services/asset_service.dart';

class WallpaperGrid extends StatelessWidget {
  final List<String> wallpapers;
  final List<String> favorites;
  final Function(String) onWallpaperTap;
  final Function(String)? onFavoriteTap;
  final bool showFavoriteIcon;

  const WallpaperGrid({
    super.key,
    required this.wallpapers,
    required this.favorites,
    required this.onWallpaperTap,
    this.onFavoriteTap,
    this.showFavoriteIcon = true,
  });

  @override
  Widget build(BuildContext context) {
    if (wallpapers.isEmpty) {
      return const Center(
        child: Column(
          mainAxisAlignment: MainAxisAlignment.center,
          children: [
            Icon(
              Icons.image_not_supported,
              size: 64,
              color: Colors.grey,
            ),
            SizedBox(height: 16),
            Text(
              'No wallpapers found',
              style: TextStyle(
                fontSize: 18,
                color: Colors.grey,
              ),
            ),
          ],
        ),
      );
    }

    return GridView.builder(
      padding: const EdgeInsets.all(8.0),
      gridDelegate: const SliverGridDelegateWithFixedCrossAxisCount(
        crossAxisCount: 2,
        crossAxisSpacing: 8.0,
        mainAxisSpacing: 8.0,
        childAspectRatio: 0.6, // Adjust for wallpaper aspect ratio
      ),
      itemCount: wallpapers.length,
      itemBuilder: (context, index) {
        final wallpaper = wallpapers[index];
        final isFavorite = favorites.contains(wallpaper);
        
        return WallpaperGridItem(
          assetPath: wallpaper,
          isFavorite: isFavorite,
          onTap: () => onWallpaperTap(wallpaper),
          onFavoriteTap: showFavoriteIcon && onFavoriteTap != null
              ? () => onFavoriteTap!(wallpaper)
              : null,
          showFavoriteIcon: showFavoriteIcon,
        );
      },
    );
  }
}

class WallpaperGridItem extends StatelessWidget {
  final String assetPath;
  final bool isFavorite;
  final VoidCallback onTap;
  final VoidCallback? onFavoriteTap;
  final bool showFavoriteIcon;

  const WallpaperGridItem({
    super.key,
    required this.assetPath,
    required this.isFavorite,
    required this.onTap,
    this.onFavoriteTap,
    this.showFavoriteIcon = true,
  });

  @override
  Widget build(BuildContext context) {
    return Card(
      elevation: 4,
      clipBehavior: Clip.antiAlias,
      child: InkWell(
        onTap: onTap,
        child: Stack(
          fit: StackFit.expand,
          children: [
            // Wallpaper image
            Image.asset(
              assetPath,
              fit: BoxFit.cover,
              errorBuilder: (context, error, stackTrace) {
                return Container(
                  color: Colors.grey[300],
                  child: const Column(
                    mainAxisAlignment: MainAxisAlignment.center,
                    children: [
                      Icon(
                        Icons.broken_image,
                        size: 48,
                        color: Colors.grey,
                      ),
                      SizedBox(height: 8),
                      Text(
                        'Image not found',
                        style: TextStyle(
                          color: Colors.grey,
                          fontSize: 12,
                        ),
                        textAlign: TextAlign.center,
                      ),
                    ],
                  ),
                );
              },
            ),
            
            // Gradient overlay for better text visibility
            Positioned(
              bottom: 0,
              left: 0,
              right: 0,
              child: Container(
                height: 60,
                decoration: BoxDecoration(
                  gradient: LinearGradient(
                    begin: Alignment.bottomCenter,
                    end: Alignment.topCenter,
                    colors: [
                      Colors.black.withOpacity(0.7),
                      Colors.transparent,
                    ],
                  ),
                ),
              ),
            ),
            
            // Wallpaper name
            Positioned(
              bottom: 8,
              left: 8,
              right: showFavoriteIcon ? 48 : 8,
              child: Text(
                AssetService.getDisplayName(assetPath),
                style: const TextStyle(
                  color: Colors.white,
                  fontSize: 12,
                  fontWeight: FontWeight.w500,
                ),
                maxLines: 2,
                overflow: TextOverflow.ellipsis,
              ),
            ),
            
            // Favorite icon
            if (showFavoriteIcon && onFavoriteTap != null)
              Positioned(
                top: 8,
                right: 8,
                child: GestureDetector(
                  onTap: onFavoriteTap,
                  child: Container(
                    padding: const EdgeInsets.all(4),
                    decoration: BoxDecoration(
                      color: Colors.black.withOpacity(0.5),
                      shape: BoxShape.circle,
                    ),
                    child: Icon(
                      isFavorite ? Icons.favorite : Icons.favorite_border,
                      color: isFavorite ? Colors.red : Colors.white,
                      size: 20,
                    ),
                  ),
                ),
              ),
          ],
        ),
      ),
    );
  }
}
